# DATABASE_URL="postgresql://postgres:admin@localhost:5432/french_lesson?schema=public"
JWT_SECRET="secret$-so_complicated"
OPENAI_API_KEY="********************************************************************************************************************************************************************"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"
# Optional: Set a default voice for TTS (alloy, echo, fable, onyx, nova, shimmer)
DEFAULT_TTS_VOICE="alloy"
NEXTAUTH_SECRET="your_nextauth_secret_here"

NEXT_PUBLIC_SUPABASE_URL=https://fvhmdgbqjoshcdbqyitx.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ2aG1kZ2Jxam9zaGNkYnF5aXR4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NTQzNzQsImV4cCI6MjA2NDIzMDM3NH0.8bOYX1_QVE0ptOeW7IkXwVAaLB-DaJYniKg74kQENPw
SUPABASE_SERVICE_ROLE_KEY="your_service_role_key_here"